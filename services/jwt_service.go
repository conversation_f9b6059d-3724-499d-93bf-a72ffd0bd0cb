package services

import (
	"api.appio.so/models"
	"api.appio.so/pkg/roles"
	"context"
	"fmt"
	propelauth "github.com/propelauth/propelauth-go/pkg"
	"go.uber.org/zap"
	"strings"
)

// JWTServiceInterface defines the interface for JWT service operations
type JWTServiceInterface interface {
	IsValidJWT(token string) bool
	ParseJWT(ctx context.Context, token string) (*models.User, roles.Role, error)
}

type JWTService struct {
	//repository repositories.UserRepository
	authClient propelauth.ClientInterface
	logger     *zap.Logger
}

func NewJWTService( /*repository repositories.UserRepository,*/ authClient propelauth.ClientInterface, logger *zap.Logger) *JWTService {
	return &JWTService{
		//repository: repository,
		authClient: authClient,
		logger:     logger,
	}
}

// Quick and light validation
func (s *JWTService) IsValidJWT(token string) bool {
	parts := strings.Split(token, ".")
	return len(parts) == 3
}

func (s *JWTService) ParseJWT(ctx context.Context, token string) (*models.User, roles.Role, error) {
	propelauthUser, err := s.authClient.GetUser(fmt.Sprintf("Bearer %s", token))
	if err != nil {
		return nil, roles.Unknown, err
	}
	_ = propelauthUser

	// TODO: store user data to DB ? and return our user

	return nil, roles.Dashboard, nil
}
