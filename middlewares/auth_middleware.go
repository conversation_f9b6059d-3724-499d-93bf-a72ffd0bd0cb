package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"context"
	"fmt"
	"github.com/appio-so/go-appioid"
	"net/http"
	"strings"
)

func AuthRoleService(apiKeyService *services.APIKeyService, jwtService *services.JWTService) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			auth := r.Header.Get("Authorization")
			if auth == "" || !strings.HasPrefix(auth, "Bearer ") {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			token := strings.TrimPrefix(auth, "Bearer ")
			if token == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			ctx := r.Context()

			var svcID *appioid.ID
			var user *models.User
			var role = roles.Unknown
			var err error

			if apiKeyService.IsValidApiKey(token) {
				svcID, role, err = apiKeyService.GetServiceIDAndRoleByAPIKey(ctx, token)
				if err != nil {
					helpers.RenderJSONError(w, r, err)
					return
				}
			} else if jwtService.IsValidJWT(token) {
				user, role, err = jwtService.ParseJWT(ctx, token)

				// TODO: do something with User from JWT. store in context?
				fmt.Printf("User: %#v \n", user)
			}

			// Attach to the context
			if svcID != nil {
				ctx = context.WithValue(ctx, SvcIDKey{}, svcID)
			}
			ctx = context.WithValue(ctx, roleKey{}, role)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
