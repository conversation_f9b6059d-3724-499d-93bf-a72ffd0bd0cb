package main

import (
	"context"
	"fmt"
	propelauth "github.com/propelauth/propelauth-go/pkg"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"api.appio.so/pkg"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/database"
	"api.appio.so/pkg/router"
	"api.appio.so/services"
	"github.com/appio-so/go-zaplog"
	"github.com/getsentry/sentry-go"
	"go.uber.org/zap"
)

/*
@title			Appio API
@version		1.0
@description	Missing API for mobile features

@host			api.appio.so
@BasePath		/

@securityDefinitions.apikey		ApiKeyAuth
@in								header
@name							Authorization
@description					Bearer token for API authorization
*/
func main() {
	cfg, err := config.LoadConfig("config")
	if err != nil {
		log.Fatalf("failed to load config: %v", err)
	}

	logger, err := zaplog.NewLogger(cfg.Server.LogLevel, cfg.Server.Env, cfg.Server.Name)
	if err != nil {
		log.Fatalf("failed to initialize logger: %v", err)
	}
	defer func() {
		if syncErr := logger.Sync(); syncErr != nil {
			log.Printf("failed to sync logger: %v", syncErr)
		}
	}()

	logger = pkg.NewSentryLogger(logger, &cfg.Server, &cfg.Sentry)
	defer sentry.Flush(2 * time.Second)

	db, dbFing, err := database.InitDatabase(context.Background(), &cfg.DB, logger)
	if err != nil {
		logger.Fatal("failed to init database", zap.Error(err))
	}
	defer db.Close()
	defer dbFing.Close()

	config.WatchConfig(cfg, logger)

	// ProperAuth for JWT authentication
	authClient, err := propelauth.InitBaseAuth(cfg.PropelAuth.URL, cfg.PropelAuth.Key, nil)
	if err != nil {
		logger.Fatal("failed to init PropelAuth", zap.Error(err))
	}

	// Initialize service container with all services
	routerServices := services.NewServiceContainer(db, dbFing, cfg, authClient, logger)

	r := router.NewRouter(router.Config{
		Logger:            logger,
		DB:                db,
		DBFing:            dbFing,
		Config:            cfg,
		Services:          routerServices,
		RateLimitRequests: 100,
		RateLimitWindow:   60,
		RateLimitBurst:    20,
	})

	// Create a server with timeouts
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start a server in a goroutine
	go func() {
		if cfg.Server.Env == "dev" {
			fmt.Printf("server started at http://localhost:%d\n", cfg.Server.Port)
		}
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("failed to start server", zap.Error(err))
		}
	}()

	// Set up a channel to listen for signals
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("shutting down server...")

	// Create a deadline context for shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	if err = srv.Shutdown(shutdownCtx); err != nil {
		logger.Fatal("server forced to shutdown", zap.Error(err))
	}

	logger.Info("server exited properly")
}
